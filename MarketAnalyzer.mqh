//+------------------------------------------------------------------+
//|                                               MarketAnalyzer.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

// Include necessary files
#include <Trade\SymbolInfo.mqh>
#include "SuperTrend.mqh"

//+------------------------------------------------------------------+
//| Class for analyzing market data                                  |
//+------------------------------------------------------------------+
class CMarketAnalyzer
{
private:
   string            m_symbol;             // Trading symbol
   CSymbolInfo       m_symbolInfo;         // Symbol information object
   CSuperTrend*      m_superTrend;         // SuperTrend indicator
   
   // Timeframe settings
   ENUM_TIMEFRAMES   m_higherTimeframe;    // Higher timeframe for analysis
   ENUM_TIMEFRAMES   m_lowerTimeframe;     // Lower timeframe for analysis
   int               m_breakoutPeriods;    // Number of periods for breakout calculation
   double            m_breakoutThreshold;  // Breakout threshold
   
   // SuperTrend settings
   bool              m_useSuperTrend;      // Use SuperTrend signals
   ENUM_TIMEFRAMES   m_superTrendTimeframe; // SuperTrend timeframe
   int               m_atrPeriod;          // ATR period for SuperTrend
   double            m_atrMultiplier;      // ATR multiplier for SuperTrend
   bool              m_changeATR;          // Use built-in ATR calculation
   bool              m_showSignals;        // Show SuperTrend signals
   
   // Price levels
   double            m_rangeHigh;          // Time-based range high
   double            m_rangeLow;           // Time-based range low
   bool              m_rangeLevelsSet;     // Flag indicating if range levels are set
   
   // Market state
   double            m_point;              // Point value
   
public:
   // Constructor
   CMarketAnalyzer(string symbol, ENUM_TIMEFRAMES higherTimeframe, ENUM_TIMEFRAMES lowerTimeframe, 
                  int breakoutPeriods, double breakoutThreshold,
                  bool useSuperTrend = false, ENUM_TIMEFRAMES superTrendTimeframe = PERIOD_M15,
                  int atrPeriod = 10, double atrMultiplier = 3.0, bool changeATR = true, bool showSignals = true)
   {
      m_symbol = symbol;
      m_higherTimeframe = higherTimeframe;
      m_lowerTimeframe = lowerTimeframe;
      m_breakoutPeriods = breakoutPeriods;
      m_breakoutThreshold = breakoutThreshold;
      m_rangeLevelsSet = false;
      
      // SuperTrend settings
      m_useSuperTrend = useSuperTrend;
      m_superTrendTimeframe = superTrendTimeframe;
      m_atrPeriod = atrPeriod;
      m_atrMultiplier = atrMultiplier;
      m_changeATR = changeATR;
      m_showSignals = showSignals;
      
      // Initialize symbol info
      if(!m_symbolInfo.Name(m_symbol))
      {
         Print("Failed to initialize symbol info for: ", m_symbol);
         return;
      }
      
      // Set point value
      m_point = m_symbolInfo.Point();
      
      // Create SuperTrend instance if enabled
      if(m_useSuperTrend)
      {
         m_superTrend = new CSuperTrend(m_symbol, m_superTrendTimeframe, m_atrPeriod, m_atrMultiplier, m_changeATR, m_showSignals);
      }
      else
      {
         m_superTrend = NULL;
      }
   }
   
   // Destructor
   ~CMarketAnalyzer() 
   {
      if(m_superTrend != NULL)
         delete m_superTrend;
   }
   
   // Initialize the market analyzer
   bool Initialize()
   {
      // Check timeframe inputs
      if(m_higherTimeframe <= m_lowerTimeframe)
      {
         Print("Error: Higher timeframe must be greater than Lower timeframe");
         return false;
      }
      
      // Initialize SuperTrend if enabled
      if(m_useSuperTrend && m_superTrend != NULL)
      {
         if(!m_superTrend.Initialize())
         {
            Print("Failed to initialize SuperTrend indicator");
            return false;
         }
      }
      
      return true;
   }
   
   // Calculate range levels within specified time window
   void CalculateRangeLevels(datetime startTime, datetime endTime)
   {
      double high = -DBL_MAX;
      double low = DBL_MAX;
      
      // Get the highest high and lowest low within the time window
      int startBar = iBarShift(m_symbol, m_higherTimeframe, startTime);
      int endBar = iBarShift(m_symbol, m_higherTimeframe, endTime);
      
      for(int i = endBar; i <= startBar; i++)
      {
         double currentHigh = iHigh(m_symbol, m_higherTimeframe, i);
         double currentLow = iLow(m_symbol, m_higherTimeframe, i);
         
         if(currentHigh > high) high = currentHigh;
         if(currentLow < low) low = currentLow;
      }
      
      m_rangeHigh = high;
      m_rangeLow = low;
      m_rangeLevelsSet = true;
      
      Print("Range levels calculated - High: ", m_rangeHigh, ", Low: ", m_rangeLow);
   }
   
   // Check for breakout signals
   // Returns: 1 for buy, -1 for sell, 0 for no signal
   int CheckBreakoutSignal()
   {
      // If range levels are not set, no signals
      if(!m_rangeLevelsSet)
         return 0;
      
      // Refresh rates
      m_symbolInfo.Refresh();
      m_symbolInfo.RefreshRates();
      
      // Get current prices
      double currentBid = m_symbolInfo.Bid();
      double currentAsk = m_symbolInfo.Ask();
      double currentPrice = (currentBid + currentAsk) / 2.0;
      
      // Calculate breakout threshold in price
      double threshold = m_breakoutThreshold * m_point;
      
      // Check for buy signal (breakout above resistance)
      if(currentPrice > m_rangeHigh + threshold)
         return 1;  // Buy signal
      
      // Check for sell signal (breakout below support)
      if(currentPrice < m_rangeLow - threshold)
         return -1; // Sell signal
      
      return 0;     // No signal
   }
   
   // Check for SuperTrend signals
   // Returns: 1 for buy, -1 for sell, 0 for no signal
   int CheckSuperTrendSignal()
   {
      if(!m_useSuperTrend || m_superTrend == NULL)
         return 0;
      
      // Calculate SuperTrend values
      if(!m_superTrend.Calculate())
         return 0;
      
      // Check for signals
      m_superTrend.CheckSignals();
      
      if(m_superTrend.IsBuySignal())
         return 1;  // Buy signal
      
      if(m_superTrend.IsSellSignal())
         return -1; // Sell signal
      
      return 0;     // No signal
   }
   
   // Get combined signal from both breakout and SuperTrend
   // Returns: 1 for buy, -1 for sell, 0 for no signal
   int GetCombinedSignal()
   {
      int breakoutSignal = CheckBreakoutSignal();
      int superTrendSignal = CheckSuperTrendSignal();
      
      // If SuperTrend is disabled, return only breakout signal
      if(!m_useSuperTrend)
         return breakoutSignal;
      
      // If both signals agree, return the signal
      if(breakoutSignal == superTrendSignal && breakoutSignal != 0)
         return breakoutSignal;
      
      // If only SuperTrend gives a signal, return it
      if(breakoutSignal == 0 && superTrendSignal != 0)
         return superTrendSignal;
      
      // If only breakout gives a signal, return it
      if(superTrendSignal == 0 && breakoutSignal != 0)
         return breakoutSignal;
      
      return 0; // No signal or conflicting signals
   }
   
   // Reset range levels (typically done at the start of a new trading session)
   void ResetRangeLevels()
   {
      m_rangeLevelsSet = false;
   }
   
   // Get range high level
   double GetRangeHigh() { return m_rangeHigh; }
   
   // Get range low level
   double GetRangeLow() { return m_rangeLow; }
   
   // Check if range levels are set
   bool AreRangeLevelsSet() { return m_rangeLevelsSet; }
   
   // Get SuperTrend value
   double GetSuperTrendValue()
   {
      if(m_useSuperTrend && m_superTrend != NULL)
         return m_superTrend.GetSuperTrendValue();
      return 0.0;
   }
   
   // Get SuperTrend trend direction
   int GetSuperTrendDirection()
   {
      if(m_useSuperTrend && m_superTrend != NULL)
         return m_superTrend.GetTrend();
      return 0;
   }
   
   // Check if it's a new bar on the specified timeframe
   bool IsNewBar(ENUM_TIMEFRAMES timeframe, datetime &lastBarTime)
   {
      datetime currentBarTime = iTime(m_symbol, timeframe, 0);
      bool newBar = (currentBarTime != lastBarTime);
      
      if(newBar)
         lastBarTime = currentBarTime;
         
      return newBar;
   }
   
   // Check if spread is acceptable
   bool IsSpreadAcceptable(int maxSpread)
   {
      m_symbolInfo.Refresh();
      return (m_symbolInfo.Spread() <= maxSpread);
   }
   
   // Enable/disable SuperTrend
   void SetUseSuperTrend(bool useSuperTrend)
   {
      if(useSuperTrend && m_superTrend == NULL)
      {
         m_superTrend = new CSuperTrend(m_symbol, m_superTrendTimeframe, m_atrPeriod, m_atrMultiplier, m_changeATR, m_showSignals);
         m_superTrend.Initialize();
      }
      else if(!useSuperTrend && m_superTrend != NULL)
      {
         delete m_superTrend;
         m_superTrend = NULL;
      }
      
      m_useSuperTrend = useSuperTrend;
   }
};