//+------------------------------------------------------------------+
//|                                                 BreakoutEA.mq5 |
//|                                    Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"
#property version   "1.00"
#property description "Multi-Timeframe Breakout EA with Trailing Stop"
#property strict

// Include necessary files
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>

// Trade objects
CTrade         Trade;
CSymbolInfo    SymbolInfo;
CPositionInfo  PositionInfo;

//--- Input Parameters: General Settings
input string   GeneralSection = "===== General Settings ====="; // General Settings
input bool     EnableTrading = true;         // Enable Trading
input double   LotSize = 0.01;               // Lot Size
input double   RiskPercent = 1.0;            // Risk Percent (0 = fixed lot)
input int      Slippage = 10;                // Slippage (in points)
input int      MagicNumber = 12345;          // Magic Number

//--- Input Parameters: Breakout Settings
input string   BreakoutSection = "===== Breakout Settings ====="; // Breakout Settings
input ENUM_TIMEFRAMES HigherTimeframe = PERIOD_H1; // Higher Timeframe
input ENUM_TIMEFRAMES LowerTimeframe = PERIOD_M15;  // Lower Timeframe
input int      BreakoutPeriods = 20;         // Number of periods for breakout calculation
input int      BreakoutThreshold = 5;        // Breakout threshold in points

//--- Input Parameters: Trade Management
input string   TradeSection = "===== Trade Management ====="; // Trade Management
input int      TakeProfit = 100;             // Take Profit (in points)
input int      StopLoss = 50;                // Stop Loss (in points)
input bool     UseTrailingStop = true;       // Use Trailing Stop
input int      TrailingStop = 30;            // Trailing Stop (in points)
input int      TrailingStep = 10;            // Trailing Step (in points)
input int      MaxSpread = 10;               // Maximum allowed spread (in points)
input bool     CloseOnOppositeSignal = true; // Close on opposite signal

//--- Input Parameters: Trading Time
input string   TimeSection = "===== Trading Time Settings ====="; // Trading Time Settings
input bool     UseTimeFilter = false;        // Use Trading Time Filter
input int      StartHour = 8;                // Trading Start Hour
input int      EndHour = 20;                 // Trading End Hour
input bool     TradingOnMonday = true;       // Allow Trading on Monday
input bool     TradingOnFriday = true;       // Allow Trading on Friday

// Global Variables
datetime lastBarTime = 0;
bool newBar = false;
double higherHigh, higherLow, lowerHigh, lowerLow;
double currentPoint;
string eaName = "BreakoutEA";

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Initialize trade object
   Trade.SetExpertMagicNumber(MagicNumber);
   Trade.SetDeviationInPoints(Slippage);
   Trade.SetMarginMode();
   Trade.SetTypeFillingBySymbol(Symbol());
   
   // Initialize symbol info
   if(!SymbolInfo.Name(Symbol()))
   {
      Print("Failed to initialize symbol info for: ", Symbol());
      return INIT_FAILED;
   }
   
   // Set current point value
   currentPoint = SymbolInfo.Point();
   
   // Check timeframe inputs
   if(HigherTimeframe <= LowerTimeframe)
   {
      Print("Error: Higher timeframe must be greater than Lower timeframe");
      return INIT_PARAMETERS_INCORRECT;
   }
   
   // Display EA information
   Comment("BreakoutEA initialized successfully\n",
           "Higher Timeframe: ", EnumToString(HigherTimeframe), "\n",
           "Lower Timeframe: ", EnumToString(LowerTimeframe));
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Comment("");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if trading is enabled
   if(!EnableTrading)
      return;
      
   // Check trading time filter
   if(!CheckTradingTime())
      return;
      
   // Check for a new bar on the lower timeframe
   CheckNewBar(LowerTimeframe);
   
   // Process only on a new bar
   if(newBar)
   {
      // Get current market data
      SymbolInfo.Refresh();
      SymbolInfo.RefreshRates();
      
      // Check maximum spread
      if(SymbolInfo.Spread() > MaxSpread)
      {
         Comment("Current spread (", SymbolInfo.Spread(), 
                 ") exceeds maximum allowed spread (", MaxSpread, ")");
         return;
      }
      
      // Calculate breakout levels
      CalculateBreakoutLevels();
      
      // Check for breakout signals
      int signal = CheckBreakoutSignal();
      
      // Process trading signals
      if(signal != 0)
      {
         // Check if we should close opposite positions
         if(CloseOnOppositeSignal)
         {
            if(signal > 0)
               ClosePositions(POSITION_TYPE_SELL);
            else if(signal < 0)
               ClosePositions(POSITION_TYPE_BUY);
         }
         
         // Open new positions based on signal
         if(signal > 0)
            OpenBuyPosition();
         else if(signal < 0)
            OpenSellPosition();
      }
      
      // Update trailing stops for open positions
      if(UseTrailingStop)
         ManageTrailingStop();
      
      // Display status
      DisplayStatus();
   }
}

//+------------------------------------------------------------------+
//| Check for a new bar                                              |
//+------------------------------------------------------------------+
void CheckNewBar(ENUM_TIMEFRAMES timeframe)
{
   datetime currentBarTime = iTime(Symbol(), timeframe, 0);
   newBar = (currentBarTime != lastBarTime);
   
   if(newBar)
      lastBarTime = currentBarTime;
}

//+------------------------------------------------------------------+
//| Calculate breakout levels based on the higher timeframe          |
//+------------------------------------------------------------------+
void CalculateBreakoutLevels()
{
   // Calculate higher timeframe high/low
   higherHigh = iHigh(Symbol(), HigherTimeframe, iHighest(Symbol(), HigherTimeframe, MODE_HIGH, BreakoutPeriods, 1));
   higherLow = iLow(Symbol(), HigherTimeframe, iLowest(Symbol(), HigherTimeframe, MODE_LOW, BreakoutPeriods, 1));
   
   // Calculate lower timeframe high/low
   lowerHigh = iHigh(Symbol(), LowerTimeframe, iHighest(Symbol(), LowerTimeframe, MODE_HIGH, BreakoutPeriods, 1));
   lowerLow = iLow(Symbol(), LowerTimeframe, iLowest(Symbol(), LowerTimeframe, MODE_LOW, BreakoutPeriods, 1));
}

//+------------------------------------------------------------------+
//| Check for breakout signals                                       |
//+------------------------------------------------------------------+
int CheckBreakoutSignal()
{
   // Get the current price
   double currentPrice = SymbolInfo.Ask();
   
   // Calculate breakout threshold in price
   double threshold = BreakoutThreshold * currentPoint;
   
   // Check for buy signal (breakout above resistance)
   if(currentPrice > higherHigh + threshold)
      return 1;  // Buy signal
   
   // Check for sell signal (breakout below support)
   if(currentPrice < higherLow - threshold)
      return -1; // Sell signal
   
   return 0;     // No signal
}

//+------------------------------------------------------------------+
//| Open a buy position                                              |
//+------------------------------------------------------------------+
void OpenBuyPosition()
{
   // Calculate lot size based on risk
   double lots = CalculateLotSize(SymbolInfo.Ask(), SymbolInfo.Ask() - StopLoss * currentPoint);
   
   // Calculate take profit and stop loss prices
   double takeProfitPrice = SymbolInfo.Ask() + TakeProfit * currentPoint;
   double stopLossPrice = SymbolInfo.Ask() - StopLoss * currentPoint;
   
   // Open buy position
   if(!Trade.Buy(lots, Symbol(), SymbolInfo.Ask(), stopLossPrice, takeProfitPrice, eaName))
   {
      Print("Error opening buy position: ", Trade.ResultComment());
      Print("Last error: ", GetLastError());
   }
   else
   {
      Print("Buy position opened successfully. Ticket: ", Trade.ResultOrder(), 
            ", Price: ", SymbolInfo.Ask(), 
            ", SL: ", stopLossPrice, 
            ", TP: ", takeProfitPrice);
   }
}

//+------------------------------------------------------------------+
//| Open a sell position                                             |
//+------------------------------------------------------------------+
void OpenSellPosition()
{
   // Calculate lot size based on risk
   double lots = CalculateLotSize(SymbolInfo.Bid(), SymbolInfo.Bid() + StopLoss * currentPoint);
   
   // Calculate take profit and stop loss prices
   double takeProfitPrice = SymbolInfo.Bid() - TakeProfit * currentPoint;
   double stopLossPrice = SymbolInfo.Bid() + StopLoss * currentPoint;
   
   // Open sell position
   if(!Trade.Sell(lots, Symbol(), SymbolInfo.Bid(), stopLossPrice, takeProfitPrice, eaName))
   {
      Print("Error opening sell position: ", Trade.ResultComment());
      Print("Last error: ", GetLastError());
   }
   else
   {
      Print("Sell position opened successfully. Ticket: ", Trade.ResultOrder(), 
            ", Price: ", SymbolInfo.Bid(), 
            ", SL: ", stopLossPrice, 
            ", TP: ", takeProfitPrice);
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                      |
//+------------------------------------------------------------------+
double CalculateLotSize(double entryPrice, double stopLossPrice)
{
   // If risk percent is 0, use fixed lot size
   if(RiskPercent <= 0)
      return LotSize;
      
   // Get account info
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * (RiskPercent / 100.0);
   
   // Calculate pip risk
   double pipRisk = MathAbs((entryPrice - stopLossPrice) / currentPoint);
   
   // Calculate pip value
   double tickSize = SymbolInfo.TickSize();
   double tickValue = SymbolInfo.TickValue();
   double pipValue = (tickValue / tickSize) * currentPoint;
   
   // Calculate lot size based on risk
   double calculatedLotSize = riskAmount / (pipRisk * pipValue);
   
   // Get the minimum and maximum lot sizes
   double minLot = SymbolInfo.LotsMin();
   double maxLot = SymbolInfo.LotsMax();
   double lotStep = SymbolInfo.LotsStep();
   
   // Normalize lot size
   calculatedLotSize = MathFloor(calculatedLotSize / lotStep) * lotStep;
   
   // Ensure lot size is within allowed range
   calculatedLotSize = MathMax(minLot, MathMin(calculatedLotSize, maxLot));
   
   return calculatedLotSize;
}

//+------------------------------------------------------------------+
//| Manage trailing stop for open positions                          |
//+------------------------------------------------------------------+
void ManageTrailingStop()
{
   // Check if there are any open positions
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      // Get position by index
      if(PositionInfo.SelectByIndex(i))
      {
         // Check if the position belongs to our EA
         if(PositionInfo.Magic() == MagicNumber && PositionInfo.Symbol() == Symbol())
         {
            // Get position ticket
            ulong ticket = PositionInfo.Ticket();
            
            // Get position type (buy or sell)
            ENUM_POSITION_TYPE posType = PositionInfo.PositionType();
            
            // Get position data
            double posOpenPrice = PositionInfo.PriceOpen();
            double posStopLoss = PositionInfo.StopLoss();
            double posTakeProfit = PositionInfo.TakeProfit();
            
            // Calculate new stop loss level based on position type
            double newStopLoss = 0.0;
            
            if(posType == POSITION_TYPE_BUY)
            {
               // For buy positions, check if price moved up enough to move trailing stop
               double potentialSL = SymbolInfo.Bid() - TrailingStop * currentPoint;
               
               // Only modify if the new stop loss is higher than the current one
               if(potentialSL > posStopLoss + TrailingStep * currentPoint)
                  newStopLoss = potentialSL;
            }
            else if(posType == POSITION_TYPE_SELL)
            {
               // For sell positions, check if price moved down enough to move trailing stop
               double potentialSL = SymbolInfo.Ask() + TrailingStop * currentPoint;
               
               // Only modify if the new stop loss is lower than the current one
               if(posStopLoss == 0 || potentialSL < posStopLoss - TrailingStep * currentPoint)
                  newStopLoss = potentialSL;
            }
            
            // Modify position if we have a new stop loss level
            if(newStopLoss > 0 && MathAbs(newStopLoss - posStopLoss) > currentPoint)
            {
               if(!Trade.PositionModify(ticket, newStopLoss, posTakeProfit))
               {
                  Print("Error modifying position #", ticket, ": ", Trade.ResultComment());
                  Print("Last error: ", GetLastError());
               }
               else
               {
                  Print("Position #", ticket, " trailing stop modified successfully to ", newStopLoss);
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Close all positions of specified type                            |
//+------------------------------------------------------------------+
void ClosePositions(ENUM_POSITION_TYPE posType)
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionInfo.SelectByIndex(i))
      {
         if(PositionInfo.Magic() == MagicNumber && 
            PositionInfo.Symbol() == Symbol() && 
            PositionInfo.PositionType() == posType)
         {
            Trade.PositionClose(PositionInfo.Ticket());
            if(Trade.ResultRetcode() != TRADE_RETCODE_DONE)
            {
               Print("Failed to close position #", PositionInfo.Ticket(), 
                     ", Error: ", Trade.ResultRetcode(), 
                     ", Description: ", Trade.ResultComment());
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Check if current time is within trading hours                    |
//+------------------------------------------------------------------+
bool CheckTradingTime()
{
   // If time filter is disabled, allow trading
   if(!UseTimeFilter)
      return true;
      
   // Get current time
   MqlDateTime dt;
   TimeCurrent(dt);
   
   // Check day of week restrictions
   if(dt.day_of_week == 1 && !TradingOnMonday)
      return false;
   if(dt.day_of_week == 5 && !TradingOnFriday)
      return false;
      
   // Check if current hour is within trading hours
   return (dt.hour >= StartHour && dt.hour < EndHour);
}

//+------------------------------------------------------------------+
//| Display status information                                       |
//+------------------------------------------------------------------+
void DisplayStatus()
{
   string status = eaName + " Status\n";
   status += "-------------------\n";
   status += "Symbol: " + Symbol() + "\n";
   status += "Spread: " + IntegerToString(SymbolInfo.Spread()) + " points\n\n";
   
   status += "Breakout Levels:\n";
   status += "Higher TF High: " + DoubleToString(higherHigh, SymbolInfo.Digits()) + "\n";
   status += "Higher TF Low: " + DoubleToString(higherLow, SymbolInfo.Digits()) + "\n";
   status += "Lower TF High: " + DoubleToString(lowerHigh, SymbolInfo.Digits()) + "\n";
   status += "Lower TF Low: " + DoubleToString(lowerLow, SymbolInfo.Digits()) + "\n\n";
   
   status += "Current Price:\n";
   status += "Bid: " + DoubleToString(SymbolInfo.Bid(), SymbolInfo.Digits()) + "\n";
   status += "Ask: " + DoubleToString(SymbolInfo.Ask(), SymbolInfo.Digits()) + "\n\n";
   
   // Display active positions
   int buyPositions = 0;
   int sellPositions = 0;
   
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionInfo.SelectByIndex(i))
      {
         if(PositionInfo.Magic() == MagicNumber && PositionInfo.Symbol() == Symbol())
         {
            if(PositionInfo.PositionType() == POSITION_TYPE_BUY)
               buyPositions++;
            else if(PositionInfo.PositionType() == POSITION_TYPE_SELL)
               sellPositions++;
         }
      }
   }
   
   status += "Active Positions:\n";
   status += "Buy: " + IntegerToString(buyPositions) + "\n";
   status += "Sell: " + IntegerToString(sellPositions) + "\n";
   
   Comment(status);
}