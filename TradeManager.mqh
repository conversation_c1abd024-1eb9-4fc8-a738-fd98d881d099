//+------------------------------------------------------------------+
//|                                                TradeManager.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

// Include necessary files
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>

//+------------------------------------------------------------------+
//| Class for managing trades                                        |
//+------------------------------------------------------------------+
class CTradeManager
{
private:
   CTrade         m_trade;           // Trade object
   CSymbolInfo    m_symbolInfo;      // Symbol information object
   CPositionInfo  m_positionInfo;    // Position information object
   
   int            m_magicNumber;     // Magic number for identification
   double         m_riskPercent;     // Risk percentage
   double         m_fixedLotSize;    // Fixed lot size
   string         m_symbol;          // Trading symbol
   double         m_point;           // Point value
   
public:
   // Constructor
   CTradeManager(int magicNumber, double riskPercent, double fixedLotSize, string symbol)
   {
      m_magicNumber = magicNumber;
      m_riskPercent = riskPercent;
      m_fixedLotSize = fixedLotSize;
      m_symbol = symbol;
      
      // Initialize trade object
      m_trade.SetExpertMagicNumber(m_magicNumber);
      
      // Initialize symbol info
      if(!m_symbolInfo.Name(m_symbol))
      {
         Print("Failed to initialize symbol info for: ", m_symbol);
         return;
      }
      
      // Set point value
      m_point = m_symbolInfo.Point();
   }
   
   // Destructor
   ~CTradeManager() {}
   
   // Initialize the trade manager
   bool Initialize(int slippage)
   {
      m_trade.SetDeviationInPoints(slippage);
      m_trade.SetMarginMode();
      m_trade.SetTypeFillingBySymbol(m_symbol);
      
      return true;
   }
   
   // Open a buy position
   bool OpenBuy(double stopLoss, double takeProfit, string comment = "")
   {
      // Refresh rates
      m_symbolInfo.Refresh();
      m_symbolInfo.RefreshRates();
      
      // Calculate lot size
      double lots = CalculateLotSize(m_symbolInfo.Ask(), m_symbolInfo.Ask() - stopLoss * m_point);
      
      // Calculate take profit and stop loss prices
      double takeProfitPrice = (takeProfit > 0) ? m_symbolInfo.Ask() + takeProfit * m_point : 0;
      double stopLossPrice = (stopLoss > 0) ? m_symbolInfo.Ask() - stopLoss * m_point : 0;
      
      // Open buy position
      bool result = m_trade.Buy(lots, m_symbol, m_symbolInfo.Ask(), stopLossPrice, takeProfitPrice, comment);
      
      if(!result)
      {
         Print("Error opening buy position: ", m_trade.ResultComment());
         Print("Last error: ", GetLastError());
         return false;
      }
      
      Print("Buy position opened successfully. Ticket: ", m_trade.ResultOrder(), 
            ", Price: ", m_symbolInfo.Ask(), 
            ", SL: ", stopLossPrice, 
            ", TP: ", takeProfitPrice);
      
      return true;
   }
   
   // Open a sell position
   bool OpenSell(double stopLoss, double takeProfit, string comment = "")
   {
      // Refresh rates
      m_symbolInfo.Refresh();
      m_symbolInfo.RefreshRates();
      
      // Calculate lot size
      double lots = CalculateLotSize(m_symbolInfo.Bid(), m_symbolInfo.Bid() + stopLoss * m_point);
      
      // Calculate take profit and stop loss prices
      double takeProfitPrice = (takeProfit > 0) ? m_symbolInfo.Bid() - takeProfit * m_point : 0;
      double stopLossPrice = (stopLoss > 0) ? m_symbolInfo.Bid() + stopLoss * m_point : 0;
      
      // Open sell position
      bool result = m_trade.Sell(lots, m_symbol, m_symbolInfo.Bid(), stopLossPrice, takeProfitPrice, comment);
      
      if(!result)
      {
         Print("Error opening sell position: ", m_trade.ResultComment());
         Print("Last error: ", GetLastError());
         return false;
      }
      
      Print("Sell position opened successfully. Ticket: ", m_trade.ResultOrder(), 
            ", Price: ", m_symbolInfo.Bid(), 
            ", SL: ", stopLossPrice, 
            ", TP: ", takeProfitPrice);
      
      return true;
   }
   
   // Close all positions of specified type
   bool ClosePositions(ENUM_POSITION_TYPE posType)
   {
      bool result = true;
      
      for(int i = PositionsTotal() - 1; i >= 0; i--)
      {
         if(m_positionInfo.SelectByIndex(i))
         {
            if(m_positionInfo.Magic() == m_magicNumber && 
               m_positionInfo.Symbol() == m_symbol && 
               m_positionInfo.PositionType() == posType)
            {
               if(!m_trade.PositionClose(m_positionInfo.Ticket()))
               {
                  Print("Failed to close position #", m_positionInfo.Ticket(), 
                        ", Error: ", m_trade.ResultRetcode(), 
                        ", Description: ", m_trade.ResultComment());
                  result = false;
               }
            }
         }
      }
      
      return result;
   }
   
   // Manage trailing stop for open positions
   bool ManageTrailingStop(int trailingStop, int trailingStep)
   {
      bool result = true;
      
      // Check if there are any open positions
      for(int i = PositionsTotal() - 1; i >= 0; i--)
      {
         // Get position by index
         if(m_positionInfo.SelectByIndex(i))
         {
            // Check if the position belongs to our EA
            if(m_positionInfo.Magic() == m_magicNumber && m_positionInfo.Symbol() == m_symbol)
            {
               // Get position ticket
               ulong ticket = m_positionInfo.Ticket();
               
               // Get position type (buy or sell)
               ENUM_POSITION_TYPE posType = m_positionInfo.PositionType();
               
               // Get position data
               double posOpenPrice = m_positionInfo.PriceOpen();
               double posStopLoss = m_positionInfo.StopLoss();
               double posTakeProfit = m_positionInfo.TakeProfit();
               
               // Refresh rates
               m_symbolInfo.Refresh();
               m_symbolInfo.RefreshRates();
               
               // Calculate new stop loss level based on position type
               double newStopLoss = 0.0;
               
               if(posType == POSITION_TYPE_BUY)
               {
                  // For buy positions, check if price moved up enough to move trailing stop
                  double potentialSL = m_symbolInfo.Bid() - trailingStop * m_point;
                  
                  // Only modify if the new stop loss is higher than the current one
                  if(potentialSL > posStopLoss + trailingStep * m_point)
                     newStopLoss = potentialSL;
               }
               else if(posType == POSITION_TYPE_SELL)
               {
                  // For sell positions, check if price moved down enough to move trailing stop
                  double potentialSL = m_symbolInfo.Ask() + trailingStop * m_point;
                  
                  // Only modify if the new stop loss is lower than the current one
                  if(posStopLoss == 0 || potentialSL < posStopLoss - trailingStep * m_point)
                     newStopLoss = potentialSL;
               }
               
               // Modify position if we have a new stop loss level
               if(newStopLoss > 0 && MathAbs(newStopLoss - posStopLoss) > m_point)
               {
                  if(!m_trade.PositionModify(ticket, newStopLoss, posTakeProfit))
                  {
                     Print("Error modifying position #", ticket, ": ", m_trade.ResultComment());
                     Print("Last error: ", GetLastError());
                     result = false;
                  }
                  else
                  {
                     Print("Position #", ticket, " trailing stop modified successfully to ", newStopLoss);
                  }
               }
            }
         }
      }
      
      return result;
   }
   
   // Calculate lot size based on risk management
   double CalculateLotSize(double entryPrice, double stopLossPrice)
   {
      // If risk percent is 0, use fixed lot size
      if(m_riskPercent <= 0)
         return m_fixedLotSize;
         
      // Get account info
      double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
      double riskAmount = accountBalance * (m_riskPercent / 100.0);
      
      // Calculate pip risk
      double pipRisk = MathAbs((entryPrice - stopLossPrice) / m_point);
      
      // Calculate pip value
      double tickSize = m_symbolInfo.TickSize();
      double tickValue = m_symbolInfo.TickValue();
      double pipValue = (tickValue / tickSize) * m_point;
      
      // Calculate lot size based on risk
      double calculatedLotSize = riskAmount / (pipRisk * pipValue);
      
      // Get the minimum and maximum lot sizes
      double minLot = m_symbolInfo.LotsMin();
      double maxLot = m_symbolInfo.LotsMax();
      double lotStep = m_symbolInfo.LotsStep();
      
      // Normalize lot size
      calculatedLotSize = MathFloor(calculatedLotSize / lotStep) * lotStep;
      
      // Ensure lot size is within allowed range
      calculatedLotSize = MathMax(minLot, MathMin(calculatedLotSize, maxLot));
      
      return calculatedLotSize;
   }
};