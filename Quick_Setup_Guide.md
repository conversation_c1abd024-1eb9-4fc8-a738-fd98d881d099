# Quick Setup Guide - Enhanced BreakoutEA

## Files Overview

### **Main Files**
- `BreakoutEA_Enhanced.mq5` - **NEW Enhanced EA** (recommended)
- `BreakoutEA_Modular.mq5` - Original modular version
- `BreakoutEA.mq5` - Original standalone version

### **Supporting Files** (Required for Enhanced version)
- `Dashboard.mqh` - **UPDATED** Enhanced dashboard
- `TradeManager.mqh` - Trade management
- `MarketAnalyzer.mqh` - Market analysis
- `TimeFilter.mqh` - Time filtering
- `SuperTrend.mqh` - SuperTrend indicator

## Quick Start (5 Minutes)

### **Step 1: Copy Files**
1. Copy all `.mq5` and `.mqh` files to your MT5 `MQL5/Experts/` folder
2. Compile `BreakoutEA_Enhanced.mq5` in MetaEditor
3. Restart MT5 if needed

### **Step 2: Basic Configuration**
```
General Settings:
✓ EnableTrading = true
✓ MagicNumber = 12345 (change to unique number)
✓ EAComment = "MyStrategy"
✓ AutoUpdateComment = true

Dashboard:
✓ ShowDashboard = true

Time Filter:
✓ UseTimeFilter = true
✓ Configure your trading hours for each day
```

### **Step 3: Attach to Chart**
1. Open your preferred currency pair chart
2. Drag `BreakoutEA_Enhanced` to chart
3. Configure parameters in popup dialog
4. Click "OK"
5. Dashboard should appear immediately

## Key Features Summary

### **✅ Enhanced Dashboard Includes:**
- **Spread**: Real-time with status (LOW/MEDIUM/HIGH)
- **SuperTrend**: Current direction and value
- **Last Trade**: Type, price, time, and total count
- **Positions**: Live count and P&L
- **Account**: Balance, equity, free margin
- **Manual Buttons**: BUY, SELL, CLOSE ALL, CLOSE BUY, CLOSE SELL
- **Comment Field**: Editable trade comments

### **✅ Complete Daily Time Settings:**
- **All 7 days**: Individual start/end times
- **Minute precision**: Hour:Minute settings
- **Overnight sessions**: Supported
- **Individual enable/disable**: Per day

### **✅ Comment Integration:**
- **Auto-combination**: Comment + Magic Number
- **Dashboard editing**: Real-time updates
- **Manual trade marking**: Automatic suffixes

## Recommended Settings

### **For Beginners**
```
LotSize = 0.01
RiskPercent = 0.5
UseSuperTrend = true
UseTimeFilter = true
ShowDashboard = true
```

### **For Experienced Traders**
```
LotSize = 0.1
RiskPercent = 2.0
UseSuperTrend = false (pure breakout)
UseTimeFilter = true
ShowDashboard = true
```

### **Conservative Settings**
```
MaxSpread = 5
StopLoss = 30
TakeProfit = 60
TrailingStop = 20
```

### **Aggressive Settings**
```
MaxSpread = 15
StopLoss = 50
TakeProfit = 100
TrailingStop = 30
```

## Daily Time Configuration Examples

### **London/New York Session**
```
Monday-Friday:
StartHour = 8, StartMinute = 0
EndHour = 20, EndMinute = 0

Saturday-Sunday:
Enabled = false
```

### **Asian Session**
```
Monday-Friday:
StartHour = 22, StartMinute = 0
EndHour = 6, EndMinute = 0 (next day)

Saturday-Sunday:
Enabled = false
```

### **24/5 Trading**
```
Monday:
StartHour = 0, StartMinute = 0
EndHour = 23, EndMinute = 59

Tuesday-Thursday:
StartHour = 0, StartMinute = 0
EndHour = 23, EndMinute = 59

Friday:
StartHour = 0, StartMinute = 0
EndHour = 22, EndMinute = 0

Saturday-Sunday:
Enabled = false
```

## Dashboard Usage

### **Information Monitoring**
- **Green numbers**: Profitable positions
- **Red numbers**: Losing positions
- **Spread status**: Avoid trading during HIGH spread

### **Manual Trading**
1. **Edit comment** in text field if desired
2. **Click BUY/SELL** for immediate trades
3. **Use CLOSE buttons** for position management
4. **Monitor P&L** in real-time

### **Comment Examples**
- Default: "MyStrategy_12345"
- Custom: "ScalpingSetup_12345"
- Manual: "MyStrategy_12345 - Manual"

## Troubleshooting

### **Dashboard Not Showing**
- Check `ShowDashboard = true`
- Restart MT5
- Check for compilation errors

### **No Trades Executing**
- Verify `EnableTrading = true`
- Check time filter settings
- Ensure spread is acceptable
- Verify account permissions

### **Wrong Trade Comments**
- Check `AutoUpdateComment` setting
- Verify comment field in dashboard
- Ensure magic number is unique

## Performance Tips

### **Optimization**
1. **Test on demo** first
2. **Start with small lots**
3. **Monitor during different sessions**
4. **Adjust time filters** based on results

### **Risk Management**
1. **Never risk more than 2%** per trade
2. **Use appropriate lot sizes**
3. **Monitor drawdown**
4. **Keep trading journal**

## Next Steps

1. **Backtest** the strategy on historical data
2. **Forward test** on demo account
3. **Gradually increase** position sizes
4. **Monitor and adjust** parameters based on performance

## Support

- Check `README_Enhanced_Features.md` for detailed documentation
- Review original EA files for comparison
- Test all features on demo account first

**Remember**: Always test thoroughly before using real money!
