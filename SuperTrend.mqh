//+------------------------------------------------------------------+
//|                                                   SuperTrend.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

//+------------------------------------------------------------------+
//| Class for SuperTrend indicator calculations                      |
//+------------------------------------------------------------------+
class CSuperTrend
{
private:
   string            m_symbol;             // Trading symbol
   ENUM_TIMEFRAMES   m_timeframe;          // Timeframe for calculations
   int               m_atrPeriod;          // ATR period
   double            m_atrMultiplier;      // ATR multiplier
   bool              m_changeATR;          // Use built-in ATR or SMA of TR
   bool              m_showSignals;        // Show buy/sell signals
   
   // Indicator handles
   int               m_atrHandle;          // ATR indicator handle
   
   // Arrays for calculations
   double            m_atrBuffer[];        // ATR values
   double            m_highBuffer[];       // High prices
   double            m_lowBuffer[];        // Low prices
   double            m_closeBuffer[];      // Close prices
   double            m_hl2Buffer[];        // HL2 values
   
   // SuperTrend values
   double            m_upTrend[];          // Upper trend line
   double            m_downTrend[];        // Lower trend line
   int               m_trend[];            // Trend direction (1 = up, -1 = down)
   
   // Signal detection
   int               m_lastTrend;          // Last trend value
   bool              m_buySignal;          // Current buy signal
   bool              m_sellSignal;         // Current sell signal
   
public:
   // Constructor
   CSuperTrend(string symbol, ENUM_TIMEFRAMES timeframe, int atrPeriod, double atrMultiplier, bool changeATR, bool showSignals)
   {
      m_symbol = symbol;
      m_timeframe = timeframe;
      m_atrPeriod = atrPeriod;
      m_atrMultiplier = atrMultiplier;
      m_changeATR = changeATR;
      m_showSignals = showSignals;
      m_lastTrend = 0;
      m_buySignal = false;
      m_sellSignal = false;
      
      // Initialize arrays
      ArraySetAsSeries(m_atrBuffer, true);
      ArraySetAsSeries(m_highBuffer, true);
      ArraySetAsSeries(m_lowBuffer, true);
      ArraySetAsSeries(m_closeBuffer, true);
      ArraySetAsSeries(m_hl2Buffer, true);
      ArraySetAsSeries(m_upTrend, true);
      ArraySetAsSeries(m_downTrend, true);
      ArraySetAsSeries(m_trend, true);
   }
   
   // Destructor
   ~CSuperTrend()
   {
      if(m_atrHandle != INVALID_HANDLE)
         IndicatorRelease(m_atrHandle);
   }
   
   // Initialize the SuperTrend indicator
   bool Initialize()
   {
      // Create ATR indicator handle
      m_atrHandle = iATR(m_symbol, m_timeframe, m_atrPeriod);
      
      if(m_atrHandle == INVALID_HANDLE)
      {
         Print("Failed to create ATR indicator handle");
         return false;
      }
      
      return true;
   }
   
   // Calculate SuperTrend values
   bool Calculate()
   {
      // Get required number of bars for calculation
      int bars = 100; // Sufficient for calculations
      
      // Copy price data
      if(CopyHigh(m_symbol, m_timeframe, 0, bars, m_highBuffer) <= 0)
         return false;
      if(CopyLow(m_symbol, m_timeframe, 0, bars, m_lowBuffer) <= 0)
         return false;
      if(CopyClose(m_symbol, m_timeframe, 0, bars, m_closeBuffer) <= 0)
         return false;
      
      // Calculate HL2
      for(int i = 0; i < bars; i++)
         m_hl2Buffer[i] = (m_highBuffer[i] + m_lowBuffer[i]) / 2.0;
      
      // Get ATR values
      if(m_changeATR)
      {
         if(CopyBuffer(m_atrHandle, 0, 0, bars, m_atrBuffer) <= 0)
            return false;
      }
      else
      {
         // Calculate SMA of True Range manually
         CalculateSMAofTR(bars);
      }
      
      // Resize arrays
      ArrayResize(m_upTrend, bars);
      ArrayResize(m_downTrend, bars);
      ArrayResize(m_trend, bars);
      
      // Calculate SuperTrend
      for(int i = bars - 1; i >= 0; i--)
      {
         // Calculate basic upper and lower bands
         double up = m_hl2Buffer[i] - (m_atrMultiplier * m_atrBuffer[i]);
         double dn = m_hl2Buffer[i] + (m_atrMultiplier * m_atrBuffer[i]);
         
         // Calculate final upper and lower bands
         if(i < bars - 1)
         {
            double up1 = m_upTrend[i + 1];
            double dn1 = m_downTrend[i + 1];
            
            up = (m_closeBuffer[i + 1] > up1) ? MathMax(up, up1) : up;
            dn = (m_closeBuffer[i + 1] < dn1) ? MathMin(dn, dn1) : dn;
         }
         
         m_upTrend[i] = up;
         m_downTrend[i] = dn;
         
         // Calculate trend
         if(i == bars - 1)
         {
            m_trend[i] = 1; // Start with uptrend
         }
         else
         {
            int prevTrend = m_trend[i + 1];
            double prevUp = m_upTrend[i + 1];
            double prevDn = m_downTrend[i + 1];
            
            if(prevTrend == -1 && m_closeBuffer[i] > prevDn)
               m_trend[i] = 1;
            else if(prevTrend == 1 && m_closeBuffer[i] < prevUp)
               m_trend[i] = -1;
            else
               m_trend[i] = prevTrend;
         }
      }
      
      return true;
   }
   
   // Check for buy/sell signals
   void CheckSignals()
   {
      m_buySignal = false;
      m_sellSignal = false;
      
      if(ArraySize(m_trend) < 2)
         return;
      
      int currentTrend = m_trend[0];
      int previousTrend = m_trend[1];
      
      // Buy signal: trend changes from -1 to 1
      if(currentTrend == 1 && previousTrend == -1)
         m_buySignal = true;
      
      // Sell signal: trend changes from 1 to -1
      if(currentTrend == -1 && previousTrend == 1)
         m_sellSignal = true;
   }
   
   // Get current trend direction
   int GetTrend()
   {
      if(ArraySize(m_trend) > 0)
         return m_trend[0];
      return 0;
   }
   
   // Get SuperTrend value for current trend
   double GetSuperTrendValue()
   {
      if(ArraySize(m_trend) > 0 && ArraySize(m_upTrend) > 0 && ArraySize(m_downTrend) > 0)
      {
         if(m_trend[0] == 1)
            return m_upTrend[0];
         else
            return m_downTrend[0];
      }
      return 0.0;
   }
   
   // Check if there's a buy signal
   bool IsBuySignal() { return m_buySignal; }
   
   // Check if there's a sell signal
   bool IsSellSignal() { return m_sellSignal; }
   
   // Get upper trend line value
   double GetUpperTrend(int shift = 0)
   {
      if(shift < ArraySize(m_upTrend))
         return m_upTrend[shift];
      return 0.0;
   }
   
   // Get lower trend line value
   double GetLowerTrend(int shift = 0)
   {
      if(shift < ArraySize(m_downTrend))
         return m_downTrend[shift];
      return 0.0;
   }
   
private:
   // Calculate Simple Moving Average of True Range
   void CalculateSMAofTR(int bars)
   {
      ArrayResize(m_atrBuffer, bars);
      
      for(int i = bars - 1; i >= 0; i--)
      {
         double sum = 0.0;
         int count = 0;
         
         for(int j = 0; j < m_atrPeriod && (i + j) < bars; j++)
         {
            double tr;
            if(i + j == bars - 1)
            {
               tr = m_highBuffer[i + j] - m_lowBuffer[i + j];
            }
            else
            {
               double high_low = m_highBuffer[i + j] - m_lowBuffer[i + j];
               double high_close = MathAbs(m_highBuffer[i + j] - m_closeBuffer[i + j + 1]);
               double low_close = MathAbs(m_lowBuffer[i + j] - m_closeBuffer[i + j + 1]);
               tr = MathMax(high_low, MathMax(high_close, low_close));
            }
            sum += tr;
            count++;
         }
         
         if(count > 0)
            m_atrBuffer[i] = sum / count;
         else
            m_atrBuffer[i] = 0.0;
      }
   }
};