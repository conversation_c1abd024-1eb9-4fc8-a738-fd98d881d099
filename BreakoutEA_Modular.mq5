//+------------------------------------------------------------------+
//|                                         BreakoutEA_Modular.mq5 |
//|                                    Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"
#property version   "1.00"
#property description "Advanced Multi-Timeframe Breakout EA with SuperTrend, Dashboard and Daily Time Settings"
#property strict

// Include necessary files
#include "TradeManager.mqh"
#include "MarketAnalyzer.mqh"
#include "TimeFilter.mqh"
#include "Dashboard.mqh"

//--- Input Parameters: General Settings
input string   GeneralSection = "===== General Settings ====="; // General Settings
input bool     EnableTrading = true;         // Enable Trading
input double   LotSize = 0.01;               // Lot Size
input double   RiskPercent = 1.0;            // Risk Percent (0 = fixed lot)
input int      Slippage = 10;                // Slippage (in points)
input int      MagicNumber = 12345;          // Magic Number
input string   EAComment = "BreakoutEA";     // EA Comment

//--- Input Parameters: Breakout Settings
input string   BreakoutSection = "===== Breakout Settings ====="; // Breakout Settings
input ENUM_TIMEFRAMES HigherTimeframe = PERIOD_H1; // Higher Timeframe
input ENUM_TIMEFRAMES LowerTimeframe = PERIOD_M15;  // Lower Timeframe
input int      BreakoutPeriods = 20;         // Number of periods for breakout calculation
input int      BreakoutThreshold = 5;        // Breakout threshold in points

//--- Input Parameters: SuperTrend Settings
input string   SuperTrendSection = "===== SuperTrend Settings ====="; // SuperTrend Settings
input bool     UseSuperTrend = false;        // Use SuperTrend Signals
input ENUM_TIMEFRAMES SuperTrendTimeframe = PERIOD_M15; // SuperTrend Timeframe
input int      ATRPeriod = 10;               // ATR Period
input double   ATRMultiplier = 3.0;          // ATR Multiplier
input bool     ChangeATR = true;             // Use Built-in ATR Calculation
input bool     ShowSuperTrendSignals = true; // Show SuperTrend Buy/Sell Signals

//--- Input Parameters: Trade Management
input string   TradeSection = "===== Trade Management ====="; // Trade Management
input int      TakeProfit = 100;             // Take Profit (in points)
input int      StopLoss = 50;                // Stop Loss (in points)
input bool     UseTrailingStop = true;       // Use Trailing Stop
input int      TrailingStop = 30;            // Trailing Stop (in points)
input int      TrailingStep = 10;            // Trailing Step (in points)
input int      MaxSpread = 10;               // Maximum allowed spread (in points)
input bool     CloseOnOppositeSignal = true; // Close on opposite signal

//--- Input Parameters: Dashboard Settings
input string   DashboardSection = "===== Dashboard Settings ====="; // Dashboard Settings
input bool     ShowDashboard = true;         // Show Dashboard

//--- Input Parameters: Daily Trading Time Settings
input string   TimeSection = "===== Daily Trading Time Settings ====="; // Daily Trading Time Settings
input bool     UseTimeFilter = true;         // Use Trading Time Filter

// Monday Settings
input string   MondaySection = "--- Monday ---"; // Monday Settings
input bool     MondayEnabled = true;         // Monday Trading Enabled
input int      MondayStartHour = 8;          // Monday Start Hour
input int      MondayStartMinute = 0;        // Monday Start Minute
input int      MondayEndHour = 20;           // Monday End Hour
input int      MondayEndMinute = 0;          // Monday End Minute

// Tuesday Settings
input string   TuesdaySection = "--- Tuesday ---"; // Tuesday Settings
input bool     TuesdayEnabled = true;        // Tuesday Trading Enabled
input int      TuesdayStartHour = 8;         // Tuesday Start Hour
input int      TuesdayStartMinute = 0;       // Tuesday Start Minute
input int      TuesdayEndHour = 20;          // Tuesday End Hour
input int      TuesdayEndMinute = 0;         // Tuesday End Minute

// Wednesday Settings
input string   WednesdaySection = "--- Wednesday ---"; // Wednesday Settings
input bool     WednesdayEnabled = true;      // Wednesday Trading Enabled
input int      WednesdayStartHour = 8;       // Wednesday Start Hour
input int      WednesdayStartMinute = 0;     // Wednesday Start Minute
input int      WednesdayEndHour = 20;        // Wednesday End Hour
input int      WednesdayEndMinute = 0;       // Wednesday End Minute

// Thursday Settings
input string   ThursdaySection = "--- Thursday ---"; // Thursday Settings
input bool     ThursdayEnabled = true;       // Thursday Trading Enabled
input int      ThursdayStartHour = 8;        // Thursday Start Hour
input int      ThursdayStartMinute = 0;      // Thursday Start Minute
input int      ThursdayEndHour = 20;         // Thursday End Hour
input int      ThursdayEndMinute = 0;        // Thursday End Minute

// Friday Settings
input string   FridaySection = "--- Friday ---"; // Friday Settings
input bool     FridayEnabled = true;         // Friday Trading Enabled
input int      FridayStartHour = 8;          // Friday Start Hour
input int      FridayStartMinute = 0;        // Friday Start Minute
input int      FridayEndHour = 20;           // Friday End Hour
input int      FridayEndMinute = 0;          // Friday End Minute

// Saturday Settings
input string   SaturdaySection = "--- Saturday ---"; // Saturday Settings
input bool     SaturdayEnabled = false;      // Saturday Trading Enabled
input int      SaturdayStartHour = 8;        // Saturday Start Hour
input int      SaturdayStartMinute = 0;      // Saturday Start Minute
input int      SaturdayEndHour = 20;         // Saturday End Hour
input int      SaturdayEndMinute = 0;        // Saturday End Minute

// Sunday Settings
input string   SundaySection = "--- Sunday ---"; // Sunday Settings
input bool     SundayEnabled = false;        // Sunday Trading Enabled
input int      SundayStartHour = 8;          // Sunday Start Hour
input int      SundayStartMinute = 0;        // Sunday Start Minute
input int      SundayEndHour = 20;           // Sunday End Hour
input int      SundayEndMinute = 0;          // Sunday End Minute

//--- Input Parameters: Range Calculation Time
input string   RangeSection = "===== Range Calculation Time ====="; // Range Time Settings
input int      RangeStartHour = 1;           // Range Start Hour
input int      RangeStartMinute = 0;         // Range Start Minute
input int      RangeEndHour = 4;             // Range End Hour
input int      RangeEndMinute = 0;           // Range End Minute

// Global Variables
datetime lastBarTime = 0;
datetime lastDayChecked = 0;
string eaName = "BreakoutEA_Advanced";

// Class instances
CTradeManager*  tradeManager;
CMarketAnalyzer* marketAnalyzer;
CTimeFilter*    timeFilter;
CDashboard*     dashboard;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Create instances of our classes
   tradeManager = new CTradeManager(MagicNumber, RiskPercent, LotSize, Symbol());
   marketAnalyzer = new CMarketAnalyzer(Symbol(), HigherTimeframe, LowerTimeframe, BreakoutPeriods, BreakoutThreshold,
                                       UseSuperTrend, SuperTrendTimeframe, ATRPeriod, ATRMultiplier, ChangeATR, ShowSuperTrendSignals);
   
   // Create time filter with daily settings
   timeFilter = new CTimeFilter(UseTimeFilter,
                               MondayEnabled, MondayStartHour, MondayStartMinute, MondayEndHour, MondayEndMinute,
                               TuesdayEnabled, TuesdayStartHour, TuesdayStartMinute, TuesdayEndHour, TuesdayEndMinute,
                               WednesdayEnabled, WednesdayStartHour, WednesdayStartMinute, WednesdayEndHour, WednesdayEndMinute,
                               ThursdayEnabled, ThursdayStartHour, ThursdayStartMinute, ThursdayEndHour, ThursdayEndMinute,
                               FridayEnabled, FridayStartHour, FridayStartMinute, FridayEndHour, FridayEndMinute,
                               SaturdayEnabled, SaturdayStartHour, SaturdayStartMinute, SaturdayEndHour, SaturdayEndMinute,
                               SundayEnabled, SundayStartHour, SundayStartMinute, SundayEndHour, SundayEndMinute,
                               RangeStartHour, RangeStartMinute, RangeEndHour, RangeEndMinute);
   
   // Create dashboard if enabled
   if(ShowDashboard)
   {
      dashboard = new CDashboard(Symbol(), MagicNumber);
      if(!dashboard.Create(ChartID(), "EADashboard", 0))
      {
         Print("Failed to create dashboard");
         delete dashboard;
         dashboard = NULL;
      }
      else
      {
         dashboard.SetComment(EAComment);
      }
   }
   else
   {
      dashboard = NULL;
   }
   
   // Initialize trade manager
   if(!tradeManager.Initialize(Slippage))
   {
      Print("Failed to initialize Trade Manager");
      return INIT_FAILED;
   }
   
   // Initialize market analyzer
   if(!marketAnalyzer.Initialize())
   {
      Print("Failed to initialize Market Analyzer");
      return INIT_FAILED;
   }
   
   // Display EA information
   string initMessage = eaName + " initialized successfully\n";
   initMessage += "Symbol: " + Symbol() + "\n";
   initMessage += "Magic Number: " + IntegerToString(MagicNumber) + "\n";
   initMessage += "Comment: " + EAComment + "\n";
   initMessage += "Higher Timeframe: " + EnumToString(HigherTimeframe) + "\n";
   initMessage += "Lower Timeframe: " + EnumToString(LowerTimeframe) + "\n";
   if(UseSuperTrend)
   {
      initMessage += "SuperTrend: ENABLED\n";
      initMessage += "SuperTrend Timeframe: " + EnumToString(SuperTrendTimeframe) + "\n";
      initMessage += "ATR Period: " + IntegerToString(ATRPeriod) + "\n";
      initMessage += "ATR Multiplier: " + DoubleToString(ATRMultiplier, 1) + "\n";
   }
   else
   {
      initMessage += "SuperTrend: DISABLED\n";
   }
   initMessage += "Dashboard: " + (ShowDashboard ? "ENABLED" : "DISABLED");
   
   Print(initMessage);
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   // Clean up our objects
   if(tradeManager != NULL)
      delete tradeManager;
   if(marketAnalyzer != NULL)
      delete marketAnalyzer;
   if(timeFilter != NULL)
      delete timeFilter;
   if(dashboard != NULL)
      delete dashboard;
   
   Comment("");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Check if trading is enabled
   if(!EnableTrading)
      return;
      
   // Check if we need to calculate new range levels (at the start of each day)
   CheckNewDay();
   
   // Check trading time filter
   if(!timeFilter.IsTradingAllowed())
   {
      // Update dashboard even when trading is not allowed
      if(dashboard != NULL)
      {
         double stValue = marketAnalyzer.GetSuperTrendValue();
         int stDirection = marketAnalyzer.GetSuperTrendDirection();
         dashboard.UpdateDashboard(stValue, stDirection);
      }
      return;
   }
      
   // Check for a new bar on the lower timeframe
   if(marketAnalyzer.IsNewBar(LowerTimeframe, lastBarTime))
   {
      // Check if spread is acceptable
      if(!marketAnalyzer.IsSpreadAcceptable(MaxSpread))
      {
         Print("Current spread exceeds maximum allowed spread (", MaxSpread, ")");
         return;
      }
      
      int signal = 0;
      
      // Get trading signals based on enabled strategies
      if(UseSuperTrend)
      {
         // Use combined signal (breakout + SuperTrend)
         signal = marketAnalyzer.GetCombinedSignal();
      }
      else
      {
         // Only process breakout trades if range levels are set and we're outside the range calculation window
         if(marketAnalyzer.AreRangeLevelsSet() && !timeFilter.IsWithinRangeWindow())
         {
            signal = marketAnalyzer.CheckBreakoutSignal();
         }
      }
      
      // Process trading signals
      if(signal != 0)
      {
         // Check if we should close opposite positions
         if(CloseOnOppositeSignal)
         {
            if(signal > 0)
               tradeManager.ClosePositions(POSITION_TYPE_SELL);
            else if(signal < 0)
               tradeManager.ClosePositions(POSITION_TYPE_BUY);
         }
         
         // Open new positions based on signal
         if(signal > 0)
         {
            if(tradeManager.OpenBuy(StopLoss, TakeProfit, EAComment))
            {
               if(dashboard != NULL)
                  dashboard.RecordLastTrade("BUY", SymbolInfoDouble(Symbol(), SYMBOL_ASK));
            }
         }
         else if(signal < 0)
         {
            if(tradeManager.OpenSell(StopLoss, TakeProfit, EAComment))
            {
               if(dashboard != NULL)
                  dashboard.RecordLastTrade("SELL", SymbolInfoDouble(Symbol(), SYMBOL_BID));
            }
         }
      }
      
      // Update trailing stops for open positions
      if(UseTrailingStop)
         tradeManager.ManageTrailingStop(TrailingStop, TrailingStep);
   }
   
   // Update dashboard
   if(dashboard != NULL)
   {
      double stValue = marketAnalyzer.GetSuperTrendValue();
      int stDirection = marketAnalyzer.GetSuperTrendDirection();
      dashboard.UpdateDashboard(stValue, stDirection);
   }
}

//+------------------------------------------------------------------+
//| Chart event function                                             |
//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   // Handle dashboard events
   if(dashboard != NULL)
      dashboard.OnEvent(id, lparam, dparam, sparam);
   
   // Handle custom events from dashboard buttons
   if(id == CHARTEVENT_CUSTOM)
   {
      if(sparam == "MANUAL_BUY")
      {
         if(tradeManager.OpenBuy(StopLoss, TakeProfit, EAComment + " - Manual"))
         {
            if(dashboard != NULL)
               dashboard.RecordLastTrade("BUY (Manual)", SymbolInfoDouble(Symbol(), SYMBOL_ASK));
         }
      }
      else if(sparam == "MANUAL_SELL")
      {
         if(tradeManager.OpenSell(StopLoss, TakeProfit, EAComment + " - Manual"))
         {
            if(dashboard != NULL)
               dashboard.RecordLastTrade("SELL (Manual)", SymbolInfoDouble(Symbol(), SYMBOL_BID));
         }
      }
      else if(sparam == "CLOSE_ALL")
      {
         tradeManager.ClosePositions(POSITION_TYPE_BUY);
         tradeManager.ClosePositions(POSITION_TYPE_SELL);
      }
      else if(sparam == "CLOSE_BUY")
      {
         tradeManager.ClosePositions(POSITION_TYPE_BUY);
      }
      else if(sparam == "CLOSE_SELL")
      {
         tradeManager.ClosePositions(POSITION_TYPE_SELL);
      }
   }
}

//+------------------------------------------------------------------+
//| Check for a new trading day                                      |
//+------------------------------------------------------------------+
void CheckNewDay()
{
   MqlDateTime dt;
   TimeCurrent(dt);
   datetime currentDay = StructToTime(dt);
   
   // Reset range levels and calculate new ones at the start of each day
   if(currentDay != lastDayChecked)
   {
      // Reset range levels
      marketAnalyzer.ResetRangeLevels();
      lastDayChecked = currentDay;
   }
   
   // Calculate range levels if we're in the range calculation window and not using SuperTrend only
   if(!UseSuperTrend && timeFilter.IsWithinRangeWindow() && !marketAnalyzer.AreRangeLevelsSet())
   {
      datetime rangeStart = timeFilter.GetRangeStartTime();
      datetime rangeEnd = timeFilter.GetRangeEndTime();
      marketAnalyzer.CalculateRangeLevels(rangeStart, rangeEnd);
   }
}