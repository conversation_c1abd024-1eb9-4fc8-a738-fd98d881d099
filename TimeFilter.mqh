//+------------------------------------------------------------------+
//|                                                 TimeFilter.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

//+------------------------------------------------------------------+
//| Structure for daily trading times                                |
//+------------------------------------------------------------------+
struct SDailyTradingTime
{
   bool     enabled;        // Trading enabled for this day
   int      startHour;      // Start hour
   int      startMinute;    // Start minute
   int      endHour;        // End hour
   int      endMinute;      // End minute
};

//+------------------------------------------------------------------+
//| Class for filtering trading times with daily settings            |
//+------------------------------------------------------------------+
class CTimeFilter
{
private:
   bool     m_useTimeFilter;      // Use trading time filter
   
   // Daily trading times (0=Sunday, 1=Monday, ..., 6=Saturday)
   SDailyTradingTime m_dailyTimes[7];
   
   // Range calculation time window
   int      m_rangeStartHour;     // Range calculation start hour
   int      m_rangeStartMinute;   // Range calculation start minute
   int      m_rangeEndHour;       // Range calculation end hour
   int      m_rangeEndMinute;     // Range calculation end minute
   
public:
   // Constructor with daily time settings
   CTimeFilter(bool useTimeFilter,
               // Monday
               bool mondayEnabled, int mondayStartHour, int mondayStartMinute, int mondayEndHour, int mondayEndMinute,
               // Tuesday
               bool tuesdayEnabled, int tuesdayStartHour, int tuesdayStartMinute, int tuesdayEndHour, int tuesdayEndMinute,
               // Wednesday
               bool wednesdayEnabled, int wednesdayStartHour, int wednesdayStartMinute, int wednesdayEndHour, int wednesdayEndMinute,
               // Thursday
               bool thursdayEnabled, int thursdayStartHour, int thursdayStartMinute, int thursdayEndHour, int thursdayEndMinute,
               // Friday
               bool fridayEnabled, int fridayStartHour, int fridayStartMinute, int fridayEndHour, int fridayEndMinute,
               // Saturday
               bool saturdayEnabled, int saturdayStartHour, int saturdayStartMinute, int saturdayEndHour, int saturdayEndMinute,
               // Sunday
               bool sundayEnabled, int sundayStartHour, int sundayStartMinute, int sundayEndHour, int sundayEndMinute,
               // Range calculation window
               int rangeStartHour, int rangeStartMinute, int rangeEndHour, int rangeEndMinute)
   {
      m_useTimeFilter = useTimeFilter;
      
      // Initialize daily trading times
      // Sunday (0)
      m_dailyTimes[0].enabled = sundayEnabled;
      m_dailyTimes[0].startHour = sundayStartHour;
      m_dailyTimes[0].startMinute = sundayStartMinute;
      m_dailyTimes[0].endHour = sundayEndHour;
      m_dailyTimes[0].endMinute = sundayEndMinute;
      
      // Monday (1)
      m_dailyTimes[1].enabled = mondayEnabled;
      m_dailyTimes[1].startHour = mondayStartHour;
      m_dailyTimes[1].startMinute = mondayStartMinute;
      m_dailyTimes[1].endHour = mondayEndHour;
      m_dailyTimes[1].endMinute = mondayEndMinute;
      
      // Tuesday (2)
      m_dailyTimes[2].enabled = tuesdayEnabled;
      m_dailyTimes[2].startHour = tuesdayStartHour;
      m_dailyTimes[2].startMinute = tuesdayStartMinute;
      m_dailyTimes[2].endHour = tuesdayEndHour;
      m_dailyTimes[2].endMinute = tuesdayEndMinute;
      
      // Wednesday (3)
      m_dailyTimes[3].enabled = wednesdayEnabled;
      m_dailyTimes[3].startHour = wednesdayStartHour;
      m_dailyTimes[3].startMinute = wednesdayStartMinute;
      m_dailyTimes[3].endHour = wednesdayEndHour;
      m_dailyTimes[3].endMinute = wednesdayEndMinute;
      
      // Thursday (4)
      m_dailyTimes[4].enabled = thursdayEnabled;
      m_dailyTimes[4].startHour = thursdayStartHour;
      m_dailyTimes[4].startMinute = thursdayStartMinute;
      m_dailyTimes[4].endHour = thursdayEndHour;
      m_dailyTimes[4].endMinute = thursdayEndMinute;
      
      // Friday (5)
      m_dailyTimes[5].enabled = fridayEnabled;
      m_dailyTimes[5].startHour = fridayStartHour;
      m_dailyTimes[5].startMinute = fridayStartMinute;
      m_dailyTimes[5].endHour = fridayEndHour;
      m_dailyTimes[5].endMinute = fridayEndMinute;
      
      // Saturday (6)
      m_dailyTimes[6].enabled = saturdayEnabled;
      m_dailyTimes[6].startHour = saturdayStartHour;
      m_dailyTimes[6].startMinute = saturdayStartMinute;
      m_dailyTimes[6].endHour = saturdayEndHour;
      m_dailyTimes[6].endMinute = saturdayEndMinute;
      
      // Range calculation window
      m_rangeStartHour = rangeStartHour;
      m_rangeStartMinute = rangeStartMinute;
      m_rangeEndHour = rangeEndHour;
      m_rangeEndMinute = rangeEndMinute;
   }
   
   // Destructor
   ~CTimeFilter() {}
   
   // Check if current time is within trading hours
   bool IsTradingAllowed()
   {
      // If time filter is disabled, allow trading
      if(!m_useTimeFilter)
         return true;
         
      // Get current time
      MqlDateTime dt;
      TimeCurrent(dt);
      
      // Get day of week (0=Sunday, 1=Monday, ..., 6=Saturday)
      int dayOfWeek = dt.day_of_week;
      
      // Check if trading is enabled for this day
      if(!m_dailyTimes[dayOfWeek].enabled)
         return false;
      
      // Check if current time is within trading hours for this day
      int currentTimeMinutes = dt.hour * 60 + dt.min;
      int startTimeMinutes = m_dailyTimes[dayOfWeek].startHour * 60 + m_dailyTimes[dayOfWeek].startMinute;
      int endTimeMinutes = m_dailyTimes[dayOfWeek].endHour * 60 + m_dailyTimes[dayOfWeek].endMinute;
      
      // Handle overnight sessions (end time next day)
      if(endTimeMinutes <= startTimeMinutes)
      {
         // Trading session spans midnight
         return (currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes);
      }
      else
      {
         // Normal trading session within the same day
         return (currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes);
      }
   }
   
   // Check if current time is within range calculation window
   bool IsWithinRangeWindow()
   {
      MqlDateTime dt;
      TimeCurrent(dt);
      
      int currentTimeMinutes = dt.hour * 60 + dt.min;
      int startTimeMinutes = m_rangeStartHour * 60 + m_rangeStartMinute;
      int endTimeMinutes = m_rangeEndHour * 60 + m_rangeEndMinute;
      
      // Handle overnight range calculation window
      if(endTimeMinutes <= startTimeMinutes)
      {
         return (currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes);
      }
      else
      {
         return (currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes);
      }
   }
   
   // Get range window start time for the current day
   datetime GetRangeStartTime()
   {
      MqlDateTime dt;
      TimeCurrent(dt);
      
      dt.hour = m_rangeStartHour;
      dt.min = m_rangeStartMinute;
      dt.sec = 0;
      
      return StructToTime(dt);
   }
   
   // Get range window end time for the current day
   datetime GetRangeEndTime()
   {
      MqlDateTime dt;
      TimeCurrent(dt);
      
      dt.hour = m_rangeEndHour;
      dt.min = m_rangeEndMinute;
      dt.sec = 0;
      
      return StructToTime(dt);
   }
   
   // Set time filter usage
   void SetUseTimeFilter(bool useTimeFilter)
   {
      m_useTimeFilter = useTimeFilter;
   }
   
   // Set trading time for a specific day
   void SetDayTradingTime(int dayOfWeek, bool enabled, int startHour, int startMinute, int endHour, int endMinute)
   {
      if(dayOfWeek >= 0 && dayOfWeek <= 6)
      {
         m_dailyTimes[dayOfWeek].enabled = enabled;
         m_dailyTimes[dayOfWeek].startHour = startHour;
         m_dailyTimes[dayOfWeek].startMinute = startMinute;
         m_dailyTimes[dayOfWeek].endHour = endHour;
         m_dailyTimes[dayOfWeek].endMinute = endMinute;
      }
   }
   
   // Set range calculation window
   void SetRangeWindow(int startHour, int startMinute, int endHour, int endMinute)
   {
      m_rangeStartHour = startHour;
      m_rangeStartMinute = startMinute;
      m_rangeEndHour = endHour;
      m_rangeEndMinute = endMinute;
   }
   
   // Get trading status for a specific day
   bool IsDayEnabled(int dayOfWeek)
   {
      if(dayOfWeek >= 0 && dayOfWeek <= 6)
         return m_dailyTimes[dayOfWeek].enabled;
      return false;
   }
   
   // Get day name
   string GetDayName(int dayOfWeek)
   {
      switch(dayOfWeek)
      {
         case 0: return "Sunday";
         case 1: return "Monday";
         case 2: return "Tuesday";
         case 3: return "Wednesday";
         case 4: return "Thursday";
         case 5: return "Friday";
         case 6: return "Saturday";
         default: return "Unknown";
      }
   }
   
   // Get current trading session info
   string GetCurrentSessionInfo()
   {
      MqlDateTime dt;
      TimeCurrent(dt);
      
      string info = "Current Day: " + GetDayName(dt.day_of_week) + "\n";
      
      if(m_dailyTimes[dt.day_of_week].enabled)
      {
         info += "Trading: ENABLED\n";
         info += "Session: " + IntegerToString(m_dailyTimes[dt.day_of_week].startHour, 2, '0') + ":" + 
                 IntegerToString(m_dailyTimes[dt.day_of_week].startMinute, 2, '0') + " - " +
                 IntegerToString(m_dailyTimes[dt.day_of_week].endHour, 2, '0') + ":" + 
                 IntegerToString(m_dailyTimes[dt.day_of_week].endMinute, 2, '0') + "\n";
         info += "Status: " + (IsTradingAllowed() ? "ACTIVE" : "INACTIVE");
      }
      else
      {
         info += "Trading: DISABLED";
      }
      
      return info;
   }
};