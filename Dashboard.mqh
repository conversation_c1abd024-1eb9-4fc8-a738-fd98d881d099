//+------------------------------------------------------------------+
//|                                                    Dashboard.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

// Include necessary files
#include <Trade\SymbolInfo.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Button.mqh>
#include <Controls\Label.mqh>
#include <Controls\Edit.mqh>

//+------------------------------------------------------------------+
//| Dashboard class for displaying EA information and controls       |
//+------------------------------------------------------------------+
class CDashboard : public CAppDialog
{
private:
   // Dashboard elements
   CLabel            m_labelSpread;
   CLabel            m_labelSuperTrend;
   CLabel            m_labelLastTrade;
   CLabel            m_labelComment;
   CButton           m_buttonBuy;
   CButton           m_buttonSell;
   CButton           m_buttonCloseAll;
   CButton           m_buttonCloseBuy;
   CButton           m_buttonCloseSell;
   CEdit             m_editComment;
   
   // Data members
   CSymbolInfo*      m_symbolInfo;
   string            m_symbol;
   int               m_magicNumber;
   datetime          m_lastTradeTime;
   string            m_lastTradeType;
   double            m_lastTradePrice;
   string            m_userComment;
   
   // Dashboard position and size
   int               m_dashboardX;
   int               m_dashboardY;
   int               m_dashboardWidth;
   int               m_dashboardHeight;
   
public:
   // Constructor
   CDashboard(string symbol, int magicNumber)
   {
      m_symbol = symbol;
      m_magicNumber = magicNumber;
      m_symbolInfo = new CSymbolInfo();
      m_symbolInfo.Name(m_symbol);
      m_lastTradeTime = 0;
      m_lastTradeType = "None";
      m_lastTradePrice = 0.0;
      m_userComment = "";
      
      // Set dashboard dimensions
      m_dashboardX = 20;
      m_dashboardY = 50;
      m_dashboardWidth = 300;
      m_dashboardHeight = 400;
   }
   
   // Destructor
   ~CDashboard()
   {
      if(m_symbolInfo != NULL)
         delete m_symbolInfo;
   }
   
   // Create dashboard
   bool Create(const long chart_id, const string name, const int subwin)
   {
      if(!CAppDialog::Create(chart_id, name, subwin, m_dashboardX, m_dashboardY, 
                            m_dashboardX + m_dashboardWidth, m_dashboardY + m_dashboardHeight))
         return false;
      
      // Set dialog properties
      SetText("EA Dashboard - " + m_symbol);
      
      // Create dashboard elements
      if(!CreateLabels())
         return false;
      if(!CreateButtons())
         return false;
      if(!CreateCommentEdit())
         return false;
      
      return true;
   }
   
   // Update dashboard information
   void UpdateDashboard(double superTrendValue, int superTrendDirection)
   {
      if(!IsVisible())
         return;
      
      // Update spread
      m_symbolInfo.Refresh();
      string spreadText = "Spread: " + IntegerToString(m_symbolInfo.Spread()) + " points";
      m_labelSpread.Text(spreadText);
      
      // Update SuperTrend
      string superTrendText = "SuperTrend: ";
      if(superTrendDirection == 1)
         superTrendText += "UP (" + DoubleToString(superTrendValue, m_symbolInfo.Digits()) + ")";
      else if(superTrendDirection == -1)
         superTrendText += "DOWN (" + DoubleToString(superTrendValue, m_symbolInfo.Digits()) + ")";
      else
         superTrendText += "NEUTRAL";
      m_labelSuperTrend.Text(superTrendText);
      
      // Update last trade info
      UpdateLastTradeInfo();
      
      // Redraw dashboard
      ChartRedraw();
   }
   
   // Set user comment
   void SetComment(string comment)
   {
      m_userComment = comment;
      m_editComment.Text(comment);
   }
   
   // Get user comment
   string GetComment()
   {
      return m_editComment.Text();
   }
   
   // Record last trade
   void RecordLastTrade(string tradeType, double price)
   {
      m_lastTradeTime = TimeCurrent();
      m_lastTradeType = tradeType;
      m_lastTradePrice = price;
   }
   
   // Handle button clicks
   virtual bool OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
   {
      if(id == CHARTEVENT_OBJECT_CLICK)
      {
         if(sparam == m_buttonBuy.Name())
         {
            OnBuyButtonClick();
            return true;
         }
         else if(sparam == m_buttonSell.Name())
         {
            OnSellButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseAll.Name())
         {
            OnCloseAllButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseBuy.Name())
         {
            OnCloseBuyButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseSell.Name())
         {
            OnCloseSellButtonClick();
            return true;
         }
      }
      
      return CAppDialog::OnEvent(id, lparam, dparam, sparam);
   }
   
private:
   // Create labels
   bool CreateLabels()
   {
      // Spread label
      if(!m_labelSpread.Create(m_chart_id, "LabelSpread", m_subwin, 10, 30, 280, 50))
         return false;
      m_labelSpread.Text("Spread: Loading...");
      if(!Add(m_labelSpread))
         return false;
      
      // SuperTrend label
      if(!m_labelSuperTrend.Create(m_chart_id, "LabelSuperTrend", m_subwin, 10, 60, 280, 80))
         return false;
      m_labelSuperTrend.Text("SuperTrend: Loading...");
      if(!Add(m_labelSuperTrend))
         return false;
      
      // Last trade label
      if(!m_labelLastTrade.Create(m_chart_id, "LabelLastTrade", m_subwin, 10, 90, 280, 130))
         return false;
      m_labelLastTrade.Text("Last Trade: None");
      if(!Add(m_labelLastTrade))
         return false;
      
      // Comment label
      if(!m_labelComment.Create(m_chart_id, "LabelComment", m_subwin, 10, 140, 280, 160))
         return false;
      m_labelComment.Text("Comment:");
      if(!Add(m_labelComment))
         return false;
      
      return true;
   }
   
   // Create buttons
   bool CreateButtons()
   {
      // Buy button
      if(!m_buttonBuy.Create(m_chart_id, "ButtonBuy", m_subwin, 10, 200, 90, 230))
         return false;
      m_buttonBuy.Text("BUY");
      m_buttonBuy.ColorBackground(clrGreen);
      m_buttonBuy.ColorBorder(clrDarkGreen);
      if(!Add(m_buttonBuy))
         return false;
      
      // Sell button
      if(!m_buttonSell.Create(m_chart_id, "ButtonSell", m_subwin, 100, 200, 180, 230))
         return false;
      m_buttonSell.Text("SELL");
      m_buttonSell.ColorBackground(clrRed);
      m_buttonSell.ColorBorder(clrDarkRed);
      if(!Add(m_buttonSell))
         return false;
      
      // Close All button
      if(!m_buttonCloseAll.Create(m_chart_id, "ButtonCloseAll", m_subwin, 190, 200, 280, 230))
         return false;
      m_buttonCloseAll.Text("CLOSE ALL");
      m_buttonCloseAll.ColorBackground(clrOrange);
      m_buttonCloseAll.ColorBorder(clrDarkOrange);
      if(!Add(m_buttonCloseAll))
         return false;
      
      // Close Buy button
      if(!m_buttonCloseBuy.Create(m_chart_id, "ButtonCloseBuy", m_subwin, 10, 240, 130, 270))
         return false;
      m_buttonCloseBuy.Text("CLOSE BUY");
      m_buttonCloseBuy.ColorBackground(clrLightGray);
      m_buttonCloseBuy.ColorBorder(clrGray);
      if(!Add(m_buttonCloseBuy))
         return false;
      
      // Close Sell button
      if(!m_buttonCloseSell.Create(m_chart_id, "ButtonCloseSell", m_subwin, 140, 240, 280, 270))
         return false;
      m_buttonCloseSell.Text("CLOSE SELL");
      m_buttonCloseSell.ColorBackground(clrLightGray);
      m_buttonCloseSell.ColorBorder(clrGray);
      if(!Add(m_buttonCloseSell))
         return false;
      
      return true;
   }
   
   // Create comment edit box
   bool CreateCommentEdit()
   {
      if(!m_editComment.Create(m_chart_id, "EditComment", m_subwin, 10, 165, 280, 190))
         return false;
      m_editComment.Text(m_userComment);
      if(!Add(m_editComment))
         return false;
      
      return true;
   }
   
   // Update last trade information
   void UpdateLastTradeInfo()
   {
      string lastTradeText = "Last Trade: ";
      if(m_lastTradeTime > 0)
      {
         lastTradeText += m_lastTradeType + " at " + DoubleToString(m_lastTradePrice, m_symbolInfo.Digits());
         lastTradeText += "\nTime: " + TimeToString(m_lastTradeTime, TIME_DATE | TIME_MINUTES);
      }
      else
      {
         lastTradeText += "None";
      }
      m_labelLastTrade.Text(lastTradeText);
   }
   
   // Button event handlers
   void OnBuyButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1001, 0, 0, "MANUAL_BUY");
   }
   
   void OnSellButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1002, 0, 0, "MANUAL_SELL");
   }
   
   void OnCloseAllButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1003, 0, 0, "CLOSE_ALL");
   }
   
   void OnCloseBuyButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1004, 0, 0, "CLOSE_BUY");
   }
   
   void OnCloseSellButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1005, 0, 0, "CLOSE_SELL");
   }
};