//+------------------------------------------------------------------+
//|                                                    Dashboard.mqh |
//|                                   Copyright 2025, Your Company |
//|                                          https://www.yourcompany.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Your Company"
#property link      "https://www.yourcompany.com"

// Include necessary files
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Controls\Dialog.mqh>
#include <Controls\Button.mqh>
#include <Controls\Label.mqh>
#include <Controls\Edit.mqh>

//+------------------------------------------------------------------+
//| Enhanced Dashboard class for displaying EA information and controls |
//+------------------------------------------------------------------+
class CDashboard : public CAppDialog
{
private:
   // Dashboard elements - Information Display
   CLabel            m_labelTitle;
   CLabel            m_labelSpread;
   CLabel            m_labelSuperTrend;
   CLabel            m_labelLastTrade;
   CLabel            m_labelPositions;
   CLabel            m_labelAccountInfo;
   CLabel            m_labelTradingStatus;
   CLabel            m_labelComment;

   // Dashboard elements - Controls
   CButton           m_buttonBuy;
   CButton           m_buttonSell;
   CButton           m_buttonCloseAll;
   CButton           m_buttonCloseBuy;
   CButton           m_buttonCloseSell;
   CEdit             m_editComment;

   // Data members
   CSymbolInfo*      m_symbolInfo;
   CPositionInfo*    m_positionInfo;
   string            m_symbol;
   int               m_magicNumber;
   datetime          m_lastTradeTime;
   string            m_lastTradeType;
   double            m_lastTradePrice;
   string            m_userComment;

   // Trading statistics
   int               m_totalTrades;
   double            m_totalProfit;

   // Dashboard position and size
   int               m_dashboardX;
   int               m_dashboardY;
   int               m_dashboardWidth;
   int               m_dashboardHeight;
   
public:
   // Constructor
   CDashboard(string symbol, int magicNumber)
   {
      m_symbol = symbol;
      m_magicNumber = magicNumber;
      m_symbolInfo = new CSymbolInfo();
      m_symbolInfo.Name(m_symbol);
      m_positionInfo = new CPositionInfo();
      m_lastTradeTime = 0;
      m_lastTradeType = "None";
      m_lastTradePrice = 0.0;
      m_userComment = "";
      m_totalTrades = 0;
      m_totalProfit = 0.0;

      // Set enhanced dashboard dimensions
      m_dashboardX = 20;
      m_dashboardY = 50;
      m_dashboardWidth = 350;
      m_dashboardHeight = 550;
   }

   // Destructor
   ~CDashboard()
   {
      if(m_symbolInfo != NULL)
         delete m_symbolInfo;
      if(m_positionInfo != NULL)
         delete m_positionInfo;
   }
   
   // Create enhanced dashboard
   bool Create(const long chart_id, const string name, const int subwin)
   {
      if(!CAppDialog::Create(chart_id, name, subwin, m_dashboardX, m_dashboardY,
                            m_dashboardX + m_dashboardWidth, m_dashboardY + m_dashboardHeight))
         return false;

      // Set dialog properties
      Caption("Enhanced EA Dashboard - " + m_symbol);

      // Create dashboard elements
      if(!CreateLabels())
         return false;
      if(!CreateButtons())
         return false;
      if(!CreateCommentEdit())
         return false;

      return true;
   }
   
   // Update enhanced dashboard information
   void UpdateDashboard(double superTrendValue, int superTrendDirection)
   {
      if(!IsVisible())
         return;

      // Update spread with color coding
      m_symbolInfo.Refresh();
      int spread = m_symbolInfo.Spread();
      string spreadText = "Spread: " + IntegerToString(spread) + " points";
      if(spread > 10)
         spreadText += " (HIGH)";
      else if(spread > 5)
         spreadText += " (MEDIUM)";
      else
         spreadText += " (LOW)";
      m_labelSpread.Text(spreadText);

      // Update SuperTrend with enhanced display
      string superTrendText = "SuperTrend: ";
      if(superTrendDirection == 1)
         superTrendText += "BULLISH (" + DoubleToString(superTrendValue, m_symbolInfo.Digits()) + ")";
      else if(superTrendDirection == -1)
         superTrendText += "BEARISH (" + DoubleToString(superTrendValue, m_symbolInfo.Digits()) + ")";
      else
         superTrendText += "NEUTRAL";
      m_labelSuperTrend.Text(superTrendText);

      // Update all information sections
      UpdateLastTradeInfo();
      UpdatePositionInfo();
      UpdateAccountInfo();
      UpdateTradingStatus();

      // Redraw dashboard
      ChartRedraw();
   }
   
   // Set user comment
   void SetComment(string comment)
   {
      m_userComment = comment;
      m_editComment.Text(comment);
   }
   
   // Get user comment
   string GetComment()
   {
      return m_editComment.Text();
   }
   
   // Record last trade with enhanced tracking
   void RecordLastTrade(string tradeType, double price)
   {
      m_lastTradeTime = TimeCurrent();
      m_lastTradeType = tradeType;
      m_lastTradePrice = price;
      m_totalTrades++;
   }

   // Update trading statistics
   void UpdateTradingStats(double profit)
   {
      m_totalProfit += profit;
   }
   
   // Handle button clicks
   virtual bool OnEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
   {
      if(id == CHARTEVENT_OBJECT_CLICK)
      {
         if(sparam == m_buttonBuy.Name())
         {
            OnBuyButtonClick();
            return true;
         }
         else if(sparam == m_buttonSell.Name())
         {
            OnSellButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseAll.Name())
         {
            OnCloseAllButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseBuy.Name())
         {
            OnCloseBuyButtonClick();
            return true;
         }
         else if(sparam == m_buttonCloseSell.Name())
         {
            OnCloseSellButtonClick();
            return true;
         }
      }
      
      return CAppDialog::OnEvent(id, lparam, dparam, sparam);
   }
   
private:
   // Create enhanced labels
   bool CreateLabels()
   {
      int yPos = 30;
      int lineHeight = 25;
      int labelWidth = 330;

      // Title label
      if(!m_labelTitle.Create(m_chart_id, "LabelTitle", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelTitle.Text("=== EA DASHBOARD - " + m_symbol + " ===");
      m_labelTitle.FontSize(10);
      if(!Add(m_labelTitle))
         return false;
      yPos += lineHeight + 5;

      // Spread label
      if(!m_labelSpread.Create(m_chart_id, "LabelSpread", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelSpread.Text("Spread: Loading...");
      if(!Add(m_labelSpread))
         return false;
      yPos += lineHeight;

      // SuperTrend label
      if(!m_labelSuperTrend.Create(m_chart_id, "LabelSuperTrend", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelSuperTrend.Text("SuperTrend: Loading...");
      if(!Add(m_labelSuperTrend))
         return false;
      yPos += lineHeight;

      // Trading status label
      if(!m_labelTradingStatus.Create(m_chart_id, "LabelTradingStatus", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelTradingStatus.Text("Trading Status: Loading...");
      if(!Add(m_labelTradingStatus))
         return false;
      yPos += lineHeight + 5;

      // Position info label (multi-line)
      if(!m_labelPositions.Create(m_chart_id, "LabelPositions", m_subwin, 10, yPos, labelWidth, yPos + lineHeight * 2))
         return false;
      m_labelPositions.Text("Positions: Loading...");
      if(!Add(m_labelPositions))
         return false;
      yPos += lineHeight * 2 + 5;

      // Account info label
      if(!m_labelAccountInfo.Create(m_chart_id, "LabelAccountInfo", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelAccountInfo.Text("Account: Loading...");
      if(!Add(m_labelAccountInfo))
         return false;
      yPos += lineHeight + 5;

      // Last trade label (multi-line)
      if(!m_labelLastTrade.Create(m_chart_id, "LabelLastTrade", m_subwin, 10, yPos, labelWidth, yPos + lineHeight * 2))
         return false;
      m_labelLastTrade.Text("Last Trade: None");
      if(!Add(m_labelLastTrade))
         return false;
      yPos += lineHeight * 2 + 5;

      // Comment label
      if(!m_labelComment.Create(m_chart_id, "LabelComment", m_subwin, 10, yPos, labelWidth, yPos + lineHeight))
         return false;
      m_labelComment.Text("Trade Comment:");
      if(!Add(m_labelComment))
         return false;

      return true;
   }
   
   // Create enhanced buttons
   bool CreateButtons()
   {
      int buttonY = 350;  // Start buttons lower to accommodate more labels
      int buttonHeight = 30;
      int buttonSpacing = 5;

      // Buy button
      if(!m_buttonBuy.Create(m_chart_id, "ButtonBuy", m_subwin, 10, buttonY, 100, buttonY + buttonHeight))
         return false;
      m_buttonBuy.Text("BUY");
      m_buttonBuy.ColorBackground(clrLimeGreen);
      m_buttonBuy.ColorBorder(clrDarkGreen);
      m_buttonBuy.FontSize(9);
      if(!Add(m_buttonBuy))
         return false;

      // Sell button
      if(!m_buttonSell.Create(m_chart_id, "ButtonSell", m_subwin, 110, buttonY, 200, buttonY + buttonHeight))
         return false;
      m_buttonSell.Text("SELL");
      m_buttonSell.ColorBackground(clrTomato);
      m_buttonSell.ColorBorder(clrDarkRed);
      m_buttonSell.FontSize(9);
      if(!Add(m_buttonSell))
         return false;

      // Close All button
      if(!m_buttonCloseAll.Create(m_chart_id, "ButtonCloseAll", m_subwin, 210, buttonY, 330, buttonY + buttonHeight))
         return false;
      m_buttonCloseAll.Text("CLOSE ALL");
      m_buttonCloseAll.ColorBackground(clrOrange);
      m_buttonCloseAll.ColorBorder(clrDarkOrange);
      m_buttonCloseAll.FontSize(9);
      if(!Add(m_buttonCloseAll))
         return false;

      buttonY += buttonHeight + buttonSpacing;

      // Close Buy button
      if(!m_buttonCloseBuy.Create(m_chart_id, "ButtonCloseBuy", m_subwin, 10, buttonY, 160, buttonY + buttonHeight))
         return false;
      m_buttonCloseBuy.Text("CLOSE BUY");
      m_buttonCloseBuy.ColorBackground(clrLightGray);
      m_buttonCloseBuy.ColorBorder(clrGray);
      m_buttonCloseBuy.FontSize(9);
      if(!Add(m_buttonCloseBuy))
         return false;

      // Close Sell button
      if(!m_buttonCloseSell.Create(m_chart_id, "ButtonCloseSell", m_subwin, 170, buttonY, 330, buttonY + buttonHeight))
         return false;
      m_buttonCloseSell.Text("CLOSE SELL");
      m_buttonCloseSell.ColorBackground(clrLightGray);
      m_buttonCloseSell.ColorBorder(clrGray);
      m_buttonCloseSell.FontSize(9);
      if(!Add(m_buttonCloseSell))
         return false;

      return true;
   }
   
   // Create enhanced comment edit box
   bool CreateCommentEdit()
   {
      int editY = 320;  // Position after labels

      if(!m_editComment.Create(m_chart_id, "EditComment", m_subwin, 10, editY, 330, editY + 25))
         return false;
      m_editComment.Text(m_userComment);
      m_editComment.FontSize(9);
      if(!Add(m_editComment))
         return false;

      return true;
   }
   
   // Update last trade information
   void UpdateLastTradeInfo()
   {
      string lastTradeText = "Last Trade: ";
      if(m_lastTradeTime > 0)
      {
         lastTradeText += m_lastTradeType + " at " + DoubleToString(m_lastTradePrice, m_symbolInfo.Digits());
         lastTradeText += "\nTime: " + TimeToString(m_lastTradeTime, TIME_DATE | TIME_MINUTES);
         lastTradeText += " | Total: " + IntegerToString(m_totalTrades);
      }
      else
      {
         lastTradeText += "None | Total: " + IntegerToString(m_totalTrades);
      }
      m_labelLastTrade.Text(lastTradeText);
   }

   // Update position information
   void UpdatePositionInfo()
   {
      int buyPositions = 0;
      int sellPositions = 0;
      double totalProfit = 0.0;

      for(int i = 0; i < PositionsTotal(); i++)
      {
         if(m_positionInfo.SelectByIndex(i))
         {
            if(m_positionInfo.Magic() == m_magicNumber && m_positionInfo.Symbol() == m_symbol)
            {
               if(m_positionInfo.PositionType() == POSITION_TYPE_BUY)
                  buyPositions++;
               else if(m_positionInfo.PositionType() == POSITION_TYPE_SELL)
                  sellPositions++;

               totalProfit += m_positionInfo.Profit() + m_positionInfo.Swap() + m_positionInfo.Commission();
            }
         }
      }

      string positionText = "Positions: BUY(" + IntegerToString(buyPositions) + ") SELL(" + IntegerToString(sellPositions) + ")";
      positionText += "\nP&L: " + DoubleToString(totalProfit, 2) + " " + AccountInfoString(ACCOUNT_CURRENCY);
      m_labelPositions.Text(positionText);
   }

   // Update account information
   void UpdateAccountInfo()
   {
      double balance = AccountInfoDouble(ACCOUNT_BALANCE);
      double equity = AccountInfoDouble(ACCOUNT_EQUITY);
      double margin = AccountInfoDouble(ACCOUNT_MARGIN);
      double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

      string accountText = "Balance: " + DoubleToString(balance, 2) + " | Equity: " + DoubleToString(equity, 2);
      accountText += " | Free Margin: " + DoubleToString(freeMargin, 2);
      m_labelAccountInfo.Text(accountText);
   }

   // Update trading status
   void UpdateTradingStatus()
   {
      MqlDateTime dt;
      TimeCurrent(dt);

      string statusText = "Status: " + GetDayName(dt.day_of_week) + " " +
                         IntegerToString(dt.hour, 2, '0') + ":" + IntegerToString(dt.min, 2, '0');
      statusText += " | Magic: " + IntegerToString(m_magicNumber);
      m_labelTradingStatus.Text(statusText);
   }

   // Get day name helper
   string GetDayName(int dayOfWeek)
   {
      switch(dayOfWeek)
      {
         case 0: return "SUN";
         case 1: return "MON";
         case 2: return "TUE";
         case 3: return "WED";
         case 4: return "THU";
         case 5: return "FRI";
         case 6: return "SAT";
         default: return "UNK";
      }
   }
   
   // Button event handlers
   void OnBuyButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1001, 0, 0, "MANUAL_BUY");
   }
   
   void OnSellButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1002, 0, 0, "MANUAL_SELL");
   }
   
   void OnCloseAllButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1003, 0, 0, "CLOSE_ALL");
   }
   
   void OnCloseBuyButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1004, 0, 0, "CLOSE_BUY");
   }
   
   void OnCloseSellButtonClick()
   {
      // This will be handled by the main EA
      EventChartCustom(m_chart_id, 1005, 0, 0, "CLOSE_SELL");
   }
};